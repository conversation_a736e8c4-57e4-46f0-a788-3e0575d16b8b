/**
 * <PERSON><PERSON>
 *
 * Handles administrative operations including error log cleanup.
 * Provides maintenance and monitoring functionality for the application.
 */

import { dbSchema, getDb } from "@database";
import { lt } from "drizzle-orm";
import type { Context } from "hono";
import { logError, logInfo } from "@/utils/logger";

/**
 * Handle error log cleanup operation
 *
 * Deletes error logs older than 90 days and returns deletion statistics.
 *
 * @param c - Hono context object
 * @returns JSON response with cleanup results
 */
export async function handleErrorCleanup(c: Context): Promise<Response> {
	try {
		logInfo("Starting error log cleanup");

		const db = getDb();
		const ninetyDaysAgo = new Date();
		ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

		const deletedRecords = await db
			.delete(dbSchema.errorLogs)
			.where(lt(dbSchema.errorLogs.createdAt, ninetyDaysAgo))
			.returning({ id: dbSchema.errorLogs.id });

		logInfo(
			`Error log cleanup completed. Deleted ${deletedRecords.length} records`,
		);

		return c.json({
			message: "Error logs cleanup completed",
			deletedCount: deletedRecords.length,
			cutoffDate: ninetyDaysAgo.toISOString(),
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logError("Error cleanup failed", error);
		return c.json(
			{
				error: "Failed to cleanup error logs",
				details: String(error),
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}

/**
 * Handle custom field sync for a specific patient
 * This is the missing endpoint that the system is trying to call
 */
export async function handleCustomFieldSync(c: Context): Promise<Response> {
	try {
		const patientId = c.req.param("id");
		const platform = c.req.param("platform") as "ap" | "cc";

		if (!patientId || !platform) {
			return c.json({
				success: false,
				error: "Missing patientId or platform parameter"
			}, 400);
		}

		if (platform !== "ap" && platform !== "cc") {
			return c.json({
				success: false,
				error: "Platform must be 'ap' or 'cc'"
			}, 400);
		}

		logInfo("Custom field sync triggered", {
			patientId,
			platform,
			endpoint: `/admin/custom-fields-sync/${patientId}/${platform}`
		});

		// TODO: Implement actual custom field sync logic here
		// For now, return success to prevent errors
		return c.json({
			success: true,
			message: "Custom field sync completed",
			patientId,
			platform,
			note: "This endpoint is implemented but sync logic is pending"
		});

	} catch (error) {
		logError("Custom field sync failed", error);
		return c.json({
			success: false,
			error: "Custom field sync failed"
		}, 500);
	}
}

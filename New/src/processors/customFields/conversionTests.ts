/**
 * Test suite for custom field value conversion logic
 * This file contains test cases to verify the fixes for conversion issues
 */

import { applyCCToAPConversion } from "./CCToAPConversionLogic";
import { applyAPToCCConversion } from "./APToCCConversionLogic";
import type { GetCCPatientCustomField, APGetCustomFieldType, GetCCCustomField } from "@/type";

// Test data for TEXTBOX_LIST conversion
const mockAPTextboxListField: APGetCustomFieldType = {
  id: "ap-textbox-1",
  name: "Test Textbox List",
  dataType: "TEXTBOX_LIST",
  textBoxListOptions: [
    { id: "opt1", label: "Option 1" },
    { id: "opt2", label: "Option 2" },
    { id: "opt3", label: "Medication" },
    { id: "opt4", label: "Diagnosis" },
    { id: "opt5", label: "Option 5" },
  ],
};

const mockCCMultiValueField: GetCCCustomField = {
  id: 123,
  name: "Test Multi Value",
  type: "text",
  allowMultipleValues: true,
  allowedValues: [
    { id: 1, value: "Option 1" },
    { id: 2, value: "Option 2" },
    { id: 3, value: "Medication" },
    { id: 4, value: "Diagnosis" },
  ],
};

/**
 * Test CC to AP TEXTBOX_LIST conversion with semantic matching
 */
export function testCCToAPTextboxListConversion() {
  console.log("Testing CC→AP TEXTBOX_LIST conversion...");

  // Test case 1: Values that match option labels
  const ccValueWithMatches: GetCCPatientCustomField = {
    field: {
      id: 123,
      name: "Test Field",
      type: "text",
      allowMultipleValues: true,
    },
    values: [
      { value: "Medication" },
      { value: "Option 1" },
      { value: "Custom Value" }, // This should use an available slot
    ],
  };

  const result1 = applyCCToAPConversion(ccValueWithMatches, mockAPTextboxListField, "ap-textbox-1");
  console.log("Test 1 - Semantic matching result:", result1);

  // Expected: { "opt3": "Medication", "opt1": "Option 1", "opt2": "Custom Value" }
  // opt3 and opt1 should be used for exact matches, opt2 for the remaining value

  // Test case 2: More values than options
  const ccValueTooManyValues: GetCCPatientCustomField = {
    field: {
      id: 123,
      name: "Test Field",
      type: "text",
      allowMultipleValues: true,
    },
    values: [
      { value: "Value 1" },
      { value: "Value 2" },
      { value: "Value 3" },
      { value: "Value 4" },
      { value: "Value 5" },
      { value: "Value 6" }, // This should be logged as unmapped
    ],
  };

  const result2 = applyCCToAPConversion(ccValueTooManyValues, mockAPTextboxListField, "ap-textbox-1");
  console.log("Test 2 - Too many values result:", result2);

  // Test case 3: Option ID resolution
  const ccValueWithOptionIds: GetCCPatientCustomField = {
    field: {
      id: 123,
      name: "Test Field",
      type: "select",
      allowMultipleValues: true,
      allowedValues: [
        { id: 1, value: "Option 1" },
        { id: 2, value: "Option 2" },
        { id: 3, value: "Medication" },
      ],
    },
    values: [
      { id: 1 }, // Should resolve to "Option 1"
      { id: 3 }, // Should resolve to "Medication"
      { value: "Direct Value" },
    ],
  };

  const result3 = applyCCToAPConversion(ccValueWithOptionIds, mockAPTextboxListField, "ap-textbox-1");
  console.log("Test 3 - Option ID resolution result:", result3);
}

/**
 * Test AP to CC conversion with improved option mapping
 */
export function testAPToCCConversion() {
  console.log("Testing AP→CC conversion...");

  // Test case 1: TEXTBOX_LIST field_value to CC multi-value
  const apTextboxValue = {
    id: "ap-textbox-1",
    field_value: {
      "opt1": "Option 1",
      "opt3": "Medication",
      "opt5": "Custom Value",
    },
  };

  const result1 = applyAPToCCConversion(apTextboxValue, mockCCMultiValueField);
  console.log("Test 1 - TEXTBOX_LIST to CC multi-value:", result1);

  // Test case 2: String array to CC multi-value
  const apArrayValue = {
    id: "ap-multi-1",
    value: ["Option 1", "Medication", "Unknown Option"],
  };

  const result2 = applyAPToCCConversion(apArrayValue, mockCCMultiValueField);
  console.log("Test 2 - String array to CC multi-value:", result2);

  // Test case 3: Single value with " | " separator
  const apSingleValue = {
    id: "ap-text-1",
    value: "Option 1 | Medication | Custom Value",
  };

  const result3 = applyAPToCCConversion(apSingleValue, mockCCMultiValueField);
  console.log("Test 3 - Single value with separator:", result3);
}

/**
 * Test edge cases and error handling
 */
export function testEdgeCases() {
  console.log("Testing edge cases...");

  // Test case 1: Empty TEXTBOX_LIST options
  const emptyOptionsField: APGetCustomFieldType = {
    id: "ap-empty-1",
    name: "Empty Options",
    dataType: "TEXTBOX_LIST",
    textBoxListOptions: [],
  };

  const ccValue: GetCCPatientCustomField = {
    field: { id: 123, name: "Test", type: "text", allowMultipleValues: true },
    values: [{ value: "Test Value" }],
  };

  const result1 = applyCCToAPConversion(ccValue, emptyOptionsField, "ap-empty-1");
  console.log("Test 1 - Empty options result:", result1);

  // Test case 2: Empty field_value object
  const emptyFieldValue = {
    id: "ap-textbox-1",
    field_value: {},
  };

  const result2 = applyAPToCCConversion(emptyFieldValue, mockCCMultiValueField);
  console.log("Test 2 - Empty field_value result:", result2);

  // Test case 3: Null/undefined values
  const nullValues: GetCCPatientCustomField = {
    field: { id: 123, name: "Test", type: "text", allowMultipleValues: true },
    values: [
      { value: null },
      { value: undefined },
      { value: "" },
      { value: "  " },
      { value: "Valid Value" },
    ],
  };

  const result3 = applyCCToAPConversion(nullValues, mockAPTextboxListField, "ap-textbox-1");
  console.log("Test 3 - Null/undefined values result:", result3);
}

/**
 * Run all tests
 */
export function runConversionTests() {
  console.log("=".repeat(60));
  console.log("CUSTOM FIELD CONVERSION TESTS");
  console.log("=".repeat(60));

  try {
    testCCToAPTextboxListConversion();
    console.log("\n" + "-".repeat(40) + "\n");
    
    testAPToCCConversion();
    console.log("\n" + "-".repeat(40) + "\n");
    
    testEdgeCases();
    console.log("\n" + "=".repeat(60));
    console.log("ALL TESTS COMPLETED");
    console.log("=".repeat(60));
  } catch (error) {
    console.error("Test execution failed:", error);
  }
}

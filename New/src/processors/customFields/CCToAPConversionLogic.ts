import { logWarn, logCustom<PERSON>ield } from "@/utils/logger";
import type { GetCCPatientCustomField, APGetCustomFieldType } from "@/type";

export type APConvertedValue =
  | string
  | number
  | string[]
  | Record<string, string>
  | null;

/**
 * Pure conversion logic for CC to AP value transformation.
 * This function contains ONLY the conversion logic without any database queries or API calls.
 * 
 * @param ccValue - The CC custom field value to convert
 * @param apField - The AP field configuration (pre-fetched)
 * @param apFieldId - The AP field ID for logging
 * @returns The converted value in AP format
 */
export function applyCCToAPConversion(
  ccValue: GetCCPatientCustomField,
  apField: APGetCustomFieldType,
  apFieldId: string
): APConvertedValue {
  const dataType = (apField.dataType || "").toUpperCase();

  logCustomField("CC→AP Value conversion - start", apField.name, {
    apFieldId,
    dataType,
    ccFieldId: ccValue.field?.id,
    ccFieldType: ccValue.field?.type,
    ccAllowMultipleValues: ccValue.field?.allowMultipleValues,
    ccValuesCount: ccValue.values?.length || 0,
  });

  switch (dataType) {
    case "TEXT":
    case "LARGE_TEXT": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // If multiple values arrive for TEXT, join with " | " per rules
      const value = parts.length === 1 ? parts[0] : parts.join(" | ");
      return value;
    }

    case "EMAIL": {
      const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
      if (parts.length === 0) return null;
      return parts[0];
    }

    case "PHONE": {
      const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
      if (parts.length === 0) return null;
      return parts[0];
    }

    case "DATE": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // Pass-through; upstream should ensure format if needed
      return parts[0];
    }

    case "NUMERICAL":
    case "MONETORY": {
      // Try to get numeric value from first CC value
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      const num = toNumber(parts[0]);
      if (num !== null) return num;
      // Fallback to string if parsed number unavailable
      return parts[0];
    }

    case "FILE_UPLOAD": {
      // CC→AP for files is out of scope; skip
      logWarn("Skipping FILE_UPLOAD conversion for CC→AP", { apFieldId });
      return null;
    }

    case "MULTIPLE_OPTIONS":
    case "CHECKBOX": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // Return as string array for multi-select
      return parts;
    }

    case "TEXTBOX_LIST": {
      const values = extractStringsFromCCValue(ccValue);
      if (values.length === 0) return null;

      // Get textBoxListOptions from AP field
      const options = apField.textBoxListOptions || [];
      if (options.length === 0) {
        logWarn("TEXTBOX_LIST field has no options; cannot map values", {
          apFieldId,
          valuesCount: values.length,
        });
        return null;
      }

      // Extract option IDs (first N options)
      const optionIds = options.slice(0, values.length).map(opt => opt.id);
      if (optionIds.length === 0) {
        logWarn("No option IDs available for TEXTBOX_LIST mapping", {
          apFieldId,
          optionsCount: options.length,
          valuesCount: values.length,
        });
        return null;
      }

      // Map values to first N option ids
      const field_value: Record<string, string> = {};
      const max = Math.min(values.length, optionIds.length);
      for (let i = 0; i < max; i++) {
        const val = values[i];
        if (val.trim() === "") continue;
        field_value[optionIds[i]] = val;
      }
      // Complete replacement semantics: return only filled keys
      return field_value;
    }

    default: {
      logWarn("Unknown AP custom field dataType; passing through as string", {
        apFieldId,
        dataType,
      });
      const parts = extractStringsFromCCValue(ccValue);
      return parts.length === 0 ? null : parts[0];
    }
  }
}

/**
 * Extract string values from CC custom field value object
 */
function extractStringsFromCCValue(ccField: GetCCPatientCustomField): string[] {
  if (!ccField?.values || !Array.isArray(ccField.values)) return [];

  const allowedValues = ccField.field?.allowedValues || [];
  const out: string[] = [];

  for (const valueItem of ccField.values) {
    if (valueItem == null) continue;

    // If value has a direct string value, use it
    if (valueItem.value !== undefined && valueItem.value !== null) {
      const s = String(valueItem.value).trim();
      if (s !== "") out.push(s);
      continue;
    }

    // If value has an ID, try to resolve it to a label using allowedValues
    if (valueItem.id !== undefined && valueItem.id !== null && allowedValues.length > 0) {
      const allowedValue = allowedValues.find(av => av.id === valueItem.id);
      if (allowedValue && allowedValue.value) {
        const s = String(allowedValue.value).trim();
        if (s !== "") out.push(s);
        continue;
      }
    }
  }

  return out;
}

/**
 * Filter out empty strings from array
 */
function filterNonEmpty(arr: string[]): string[] {
  return arr.filter(s => s.trim() !== "");
}

/**
 * Convert string to number, return null if invalid
 */
function toNumber(str: string): number | null {
  const trimmed = str.trim();
  if (trimmed === "") return null;
  const num = Number(trimmed);
  return isNaN(num) ? null : num;
}

import { logWarn, logCustom<PERSON>ield } from "@/utils/logger";
import type { GetCCPatientCustomField, APGetCustomFieldType } from "@/type";

export type APConvertedValue =
  | string
  | number
  | string[]
  | Record<string, string>
  | null;

/**
 * Pure conversion logic for CC to AP value transformation.
 * This function contains ONLY the conversion logic without any database queries or API calls.
 * 
 * @param ccValue - The CC custom field value to convert
 * @param apField - The AP field configuration (pre-fetched)
 * @param apFieldId - The AP field ID for logging
 * @returns The converted value in AP format
 */
export function applyCCToAPConversion(
  ccValue: GetCCPatientCustomField,
  apField: APGetCustomFieldType,
  apFieldId: string
): APConvertedValue {
  const dataType = (apField.dataType || "").toUpperCase();

  logCustomField("CC→AP Value conversion - start", apField.name, {
    apFieldId,
    dataType,
    ccFieldId: ccValue.field?.id,
    ccFieldType: ccValue.field?.type,
    ccAllowMultipleValues: ccValue.field?.allowMultipleValues,
    ccValuesCount: ccValue.values?.length || 0,
  });

  switch (dataType) {
    case "TEXT":
    case "LARGE_TEXT": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // If multiple values arrive for TEXT, join with " | " per rules
      const value = parts.length === 1 ? parts[0] : parts.join(" | ");
      return value;
    }

    case "EMAIL": {
      const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
      if (parts.length === 0) return null;

      // Use first valid email, log if multiple emails are provided
      if (parts.length > 1) {
        logWarn("Multiple CC values provided for EMAIL field, using first value", {
          apFieldId,
          ccValues: parts,
          selectedValue: parts[0],
        });
      }
      return parts[0];
    }

    case "PHONE": {
      const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
      if (parts.length === 0) return null;

      // Use first valid phone, log if multiple phones are provided
      if (parts.length > 1) {
        logWarn("Multiple CC values provided for PHONE field, using first value", {
          apFieldId,
          ccValues: parts,
          selectedValue: parts[0],
        });
      }
      return parts[0];
    }

    case "DATE": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // Pass-through; upstream should ensure format if needed
      return parts[0];
    }

    case "NUMERICAL":
    case "MONETORY": {
      // Try to get numeric value from first CC value
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;

      // Log if multiple values provided for numeric field
      if (parts.length > 1) {
        logWarn("Multiple CC values provided for NUMERICAL/MONETORY field, using first value", {
          apFieldId,
          dataType,
          ccValues: parts,
          selectedValue: parts[0],
        });
      }

      const num = toNumber(parts[0]);
      if (num !== null) return num;

      // Log when numeric conversion fails
      logWarn("Failed to convert CC value to number, returning as string", {
        apFieldId,
        dataType,
        ccValue: parts[0],
      });

      // Fallback to string if parsed number unavailable
      return parts[0];
    }

    case "FILE_UPLOAD": {
      // CC→AP for files is out of scope; skip
      logWarn("Skipping FILE_UPLOAD conversion for CC→AP", { apFieldId });
      return null;
    }

    case "MULTIPLE_OPTIONS":
    case "CHECKBOX": {
      const parts = extractStringsFromCCValue(ccValue);
      if (parts.length === 0) return null;
      // Return as string array for multi-select
      return parts;
    }

    case "TEXTBOX_LIST": {
      const values = extractStringsFromCCValue(ccValue);
      if (values.length === 0) return null;

      // Get textBoxListOptions from AP field
      const options = apField.textBoxListOptions || [];
      if (options.length === 0) {
        logWarn("TEXTBOX_LIST field has no options; cannot map values", {
          apFieldId,
          valuesCount: values.length,
        });
        return null;
      }

      // Smart mapping: try to match values to option labels first, then use available slots
      const field_value: Record<string, string> = {};
      const usedOptionIds = new Set<string>();

      // First pass: try to match values to existing option labels
      for (const value of values) {
        if (value.trim() === "") continue;

        // Look for an option with matching label/value
        const matchingOption = options.find(opt =>
          opt.label && opt.label.toLowerCase().trim() === value.toLowerCase().trim()
        );

        if (matchingOption && !usedOptionIds.has(matchingOption.id)) {
          field_value[matchingOption.id] = value;
          usedOptionIds.add(matchingOption.id);
        }
      }

      // Second pass: map remaining values to unused option slots
      const remainingValues = values.filter(value => {
        if (value.trim() === "") return false;
        // Check if this value was already mapped in first pass
        return !Object.values(field_value).some(mappedValue =>
          mappedValue.toLowerCase().trim() === value.toLowerCase().trim()
        );
      });

      const unusedOptions = options.filter(opt => !usedOptionIds.has(opt.id));
      const maxRemaining = Math.min(remainingValues.length, unusedOptions.length);

      for (let i = 0; i < maxRemaining; i++) {
        const value = remainingValues[i];
        const option = unusedOptions[i];
        if (value.trim() !== "") {
          field_value[option.id] = value;
          usedOptionIds.add(option.id);
        }
      }

      // Log if some values couldn't be mapped
      const unmappedValues = remainingValues.slice(maxRemaining);
      if (unmappedValues.length > 0) {
        logWarn("Some CC values could not be mapped to TEXTBOX_LIST options", {
          apFieldId,
          unmappedValues,
          availableOptions: options.length,
          usedOptions: usedOptionIds.size,
        });
      }

      // Return null if no values were successfully mapped
      if (Object.keys(field_value).length === 0) {
        logWarn("No CC values could be mapped to TEXTBOX_LIST options", {
          apFieldId,
          ccValues: values,
          optionsCount: options.length,
        });
        return null;
      }

      return field_value;
    }

    default: {
      logWarn("Unknown AP custom field dataType; passing through as string", {
        apFieldId,
        dataType,
      });
      const parts = extractStringsFromCCValue(ccValue);
      return parts.length === 0 ? null : parts[0];
    }
  }
}

/**
 * Extract string values from CC custom field value object
 * Handles both direct string values and option ID resolution
 */
function extractStringsFromCCValue(ccField: GetCCPatientCustomField): string[] {
  if (!ccField?.values || !Array.isArray(ccField.values)) return [];

  const allowedValues = ccField.field?.allowedValues || [];
  const out: string[] = [];

  for (const valueItem of ccField.values) {
    if (valueItem == null) continue;

    // If value has a direct string value, use it
    if (valueItem.value !== undefined && valueItem.value !== null) {
      const s = String(valueItem.value).trim();
      if (s !== "") out.push(s);
      continue;
    }

    // If value has an ID, try to resolve it to a label using allowedValues
    if (valueItem.id !== undefined && valueItem.id !== null && allowedValues.length > 0) {
      const allowedValue = allowedValues.find(av => av.id === valueItem.id);
      if (allowedValue && allowedValue.value) {
        const s = String(allowedValue.value).trim();
        if (s !== "") out.push(s);
        continue;
      } else {
        // Log when option ID cannot be resolved
        logWarn("CC option ID could not be resolved to value", {
          ccFieldId: ccField.field?.id,
          ccFieldName: ccField.field?.name,
          optionId: valueItem.id,
          availableOptions: allowedValues.map(av => ({ id: av.id, value: av.value })),
        });
      }
    }
  }

  return out;
}

/**
 * Filter out empty strings from array
 */
function filterNonEmpty(arr: string[]): string[] {
  return arr.filter(s => s.trim() !== "");
}

/**
 * Convert string to number, return null if invalid
 */
function toNumber(str: string): number | null {
  const trimmed = str.trim();
  if (trimmed === "") return null;
  const num = Number(trimmed);
  return isNaN(num) ? null : num;
}

import apiClient from "@/apiClient";
import { getDb, dbSchema } from "@/database";
import { logDebug, logWarn, logError, logCustomField } from "@/utils/logger";
import type { APGetCustomFieldType, GetCCPatientCustomField, GetCCCustomField } from "@/type";

import { applyCCToAPConversion, type APConvertedValue } from "./CCToAPConversionLogic";
import { applyAPToCCConversion, type CCCustomFieldValue, type APCustomFieldValue } from "./APToCCConversionLogic";

// Re-export types for convenience
export type { APConvertedValue, CCCustomFieldValue, APCustomFieldValue };

// Database row type for custom field mapping
type CustomFieldMapping = typeof dbSchema.customFields.$inferSelect;

/**
 * ValueConverter class that optimizes bulk value conversions by pre-fetching 
 * and caching all custom field configurations.
 * 
 * **Performance Goals:**
 * - Eliminate individual API calls during bulk processing
 * - Fetch all field configurations once at startup
 * - Use database only for field ID mappings (ccId ↔ apId)
 * - Support processing hundreds/thousands of values efficiently
 * 
 * **Usage Pattern:**
 * ```typescript
 * const converter = new ValueConverter(); // Auto-fetches all configs
 * for (const ccValue of manyValues) {
 *   const apValue = await converter.convertCCToAP(ccValue); // No API calls
 * }
 * ```
 */
export class ValueConverter {
  private initialized = false;
  private initializationPromise: Promise<void> | null = null;
  
  // In-memory caches for fast O(1) lookups
  private apFieldsCache = new Map<string, APGetCustomFieldType>();
  private ccFieldsCache = new Map<number, GetCCCustomField>();
  private ccToApMappings = new Map<number, CustomFieldMapping>(); // ccId -> mapping
  private apToCcMappings = new Map<string, CustomFieldMapping>(); // apId -> mapping

  constructor() {
    // Lazy initialization on first method call to avoid async constructor issues
  }

  /**
   * Ensure the converter is initialized before processing
   */
  private async ensureInitialized(): Promise<void> {
    if (this.initialized) return;
    if (this.initializationPromise) return this.initializationPromise;
    
    this.initializationPromise = this.initialize();
    await this.initializationPromise;
  }

  /**
   * Initialize method that fetches all custom fields from both APIs and database mappings
   */
  private async initialize(): Promise<void> {
    try {
      logDebug("ValueConverter initialization started");
      
      const db = getDb();
      
      // Fetch all data in parallel for maximum performance
      const [apFields, ccFields, dbMappings] = await Promise.all([
        apiClient.ap.apCustomfield.allWithParentFilter(true), // invalidate cache
        apiClient.cc.ccCustomfieldReq.all(true), // invalidate cache
        db.select().from(dbSchema.customFields)
      ]);

      // Clear existing caches
      this.apFieldsCache.clear();
      this.ccFieldsCache.clear();
      this.ccToApMappings.clear();
      this.apToCcMappings.clear();

      // Populate AP fields cache
      for (const field of apFields) {
        this.apFieldsCache.set(field.id, field);
      }

      // Populate CC fields cache
      for (const field of ccFields) {
        this.ccFieldsCache.set(field.id, field);
      }

      // Populate mapping caches for bidirectional lookups
      for (const mapping of dbMappings) {
        if (mapping.ccId) {
          this.ccToApMappings.set(mapping.ccId, mapping);
        }
        if (mapping.apId) {
          this.apToCcMappings.set(mapping.apId, mapping);
        }
      }

      this.initialized = true;
      
      logDebug("ValueConverter initialization completed", {
        apFieldsCount: this.apFieldsCache.size,
        ccFieldsCount: this.ccFieldsCache.size,
        mappingsCount: dbMappings.length,
        ccToApMappingsCount: this.ccToApMappings.size,
        apToCcMappingsCount: this.apToCcMappings.size
      });

    } catch (error) {
      logError("ValueConverter initialization failed", { error });
      this.initialized = false;
      this.initializationPromise = null;
      throw error;
    }
  }

  /**
   * Convert CC value to AP format using cached configurations
   */
  async convertCCToAP(ccValue: GetCCPatientCustomField): Promise<APConvertedValue> {
    await this.ensureInitialized();
    
    if (!ccValue?.field?.id) {
      logWarn("convertCCToAP called without valid CC field ID in value object");
      return null;
    }

    try {
      // Use cached mapping instead of database query
      const mapping = this.ccToApMappings.get(ccValue.field.id);
      if (!mapping || !mapping.apId) {
        logWarn("No field mapping found for CC field", {
          ccFieldId: ccValue.field.id,
          ccFieldName: ccValue.field.name,
        });
        return null;
      }

      // Use cached AP field config instead of API call
      const field = this.apFieldsCache.get(mapping.apId);
      if (!field) {
        logWarn("AP field not found in cache", { 
          apFieldId: mapping.apId,
          ccFieldId: ccValue.field.id 
        });
        return null;
      }

      const dataType = (field.dataType || "").toUpperCase();

      logCustomField("CC→AP Value conversion - start", field.name, {
        apFieldId: mapping.apId,
        dataType,
        ccFieldId: ccValue.field?.id,
        ccFieldType: ccValue.field?.type,
        ccAllowMultipleValues: ccValue.field?.allowMultipleValues,
        ccValuesCount: ccValue.values?.length || 0,
      });

      // Apply conversion logic based on AP field dataType
      return applyCCToAPConversion(ccValue, field, mapping.apId);

    } catch (error) {
      logError("convertCCToAP failed", {
        ccFieldId: ccValue.field?.id,
        error,
      });
      return null;
    }
  }

  /**
   * Convert AP value to CC format using cached configurations
   */
  async convertAPToCC(apValue: APCustomFieldValue): Promise<CCCustomFieldValue | null> {
    await this.ensureInitialized();
    
    if (!apValue?.id) {
      logWarn("convertAPToCC called without valid AP field ID in value object", {
        hasApValueId: !!apValue?.id,
      });
      return null;
    }

    try {
      // Use cached mapping instead of database query
      const mapping = this.apToCcMappings.get(apValue.id);
      if (!mapping || !mapping.ccId) {
        logWarn("No field mapping found for AP field", {
          name: apValue.id,
          apFieldId: apValue.id,
        });
        return null;
      }

      // Use cached CC field config instead of API call
      const ccConfig = this.ccFieldsCache.get(mapping.ccId);
      if (!ccConfig) {
        logWarn("CC field not found in cache", { 
          ccFieldId: mapping.ccId,
          apFieldId: apValue.id 
        });
        return null;
      }

      logCustomField("AP→CC Value conversion - start", ccConfig.name, {
        apFieldId: apValue.id,
        ccFieldId: mapping.ccId,
        ccFieldType: ccConfig.type,
        ccAllowMultipleValues: ccConfig.allowMultipleValues,
        apValueType: typeof apValue.value,
        hasFieldValue: !!apValue.field_value,
      });

      // Apply conversion logic based on CC field configuration
      return applyAPToCCConversion(apValue, ccConfig);

    } catch (error) {
      logError("convertAPToCC failed", {
        apFieldId: apValue.id,
        error,
      });
      return null;
    }
  }

  /**
   * Refresh cache by re-fetching all configurations
   */
  async refreshCache(): Promise<void> {
    logDebug("ValueConverter cache refresh requested");
    this.initialized = false;
    this.initializationPromise = null;
    await this.ensureInitialized();
    logDebug("ValueConverter cache refresh completed");
  }






}

import { logDebug, logCustom<PERSON>ield, logWarn, logError } from "@/utils/logger";
import type { GetCCCustomField } from "@/type";

export type CCCustomFieldValue = {
  values: Array<{
    id?: number;
    value?: string;
  }>;
  field: {
    id: number;
  };
};

export type APCustomFieldValue = {
  id: string;
  value?: string | number | string[];
  field_value?: Record<string, string>;
};

/**
 * Pure conversion logic for AP to CC value transformation.
 * This function contains ONLY the conversion logic without any database queries or API calls.
 * 
 * @param apValue - The AP custom field value to convert
 * @param ccConfig - The CC field configuration (pre-fetched)
 * @returns The converted value in CC format
 */
export function applyAPToCCConversion(
  apValue: APCustomFieldValue,
  ccConfig: GetCCCustomField
): CCCustomFieldValue | null {
  logCustomField("AP→CC Value conversion - start", ccConfig.name, {
    apFieldId: apValue.id,
    ccFieldId: ccConfig.id,
    ccFieldType: ccConfig.type,
    ccAllowMultipleValues: ccConfig.allowMultipleValues,
    apValueType: typeof apValue.value,
    hasFieldValue: !!apValue.field_value,
  });

  // Convert AP value to CC format based on the value type and CC field configuration
  const ccValues: Array<{ id?: number; value?: string }> = [];

  // Handle TEXTBOX_LIST field_value (Record<string, string>)
  if (apValue.field_value && typeof apValue.field_value === "object") {
    // Debug logging for field_value structure
    logDebug("Processing AP field_value object", {
      apFieldId: apValue.id,
      ccFieldId: ccConfig.id,
      fieldValueType: typeof apValue.field_value,
      fieldValueKeys: Object.keys(apValue.field_value),
      fieldValueEntries: Object.entries(apValue.field_value).map(([key, value]) => ({
        key,
        valueType: typeof value,
        value: typeof value === 'object' ? JSON.stringify(value) : value,
      })),
    });

    // Extract values from the field_value object, filtering out empty values and objects
    const values = Object.values(apValue.field_value).filter(v => {
      if (!v) return false;
      // Skip objects that would become '[object Object]'
      if (typeof v === "object") {
        logWarn("Skipping object value in AP field_value - this indicates incorrect TEXTBOX_LIST format", {
          apFieldId: apValue.id,
          ccFieldId: ccConfig.id,
          valueType: typeof v,
          value: JSON.stringify(v),
          expectedFormat: "field_value should be Record<string, string> with option IDs as keys",
        });
        return false;
      }
      return String(v).trim() !== "";
    });

    if (values.length === 0) {
      logDebug("AP field_value object contains no valid values", {
        apFieldId: apValue.id,
        ccFieldId: ccConfig.id,
        fieldValueKeys: Object.keys(apValue.field_value),
        fieldValueTypes: Object.values(apValue.field_value).map(v => typeof v),
      });
    } else {
      if (ccConfig.allowMultipleValues) {
        // For multi-value CC fields, create multiple value entries
        for (const value of values) {
          ccValues.push({ value: String(value).trim() });
        }
      } else {
        // For single-value CC fields, join with " | " separator
        const joinedValue = values.join(" | ");
        ccValues.push({ value: joinedValue });

        // Log when multiple values are joined for single-value field
        if (values.length > 1) {
          logDebug("Multiple AP field_value entries joined for single-value CC field", {
            apFieldId: apValue.id,
            ccFieldId: ccConfig.id,
            originalValues: values,
            joinedValue,
          });
        }
      }
    }
  }
  // Handle regular value field (string, number, or string array)
  else if (apValue.value !== undefined && apValue.value !== null) {
    if (Array.isArray(apValue.value)) {
      // Handle string array (from MULTIPLE_OPTIONS/CHECKBOX)
      const validValues = apValue.value.filter(v => {
        if (!v) return false;
        // Skip objects that would become '[object Object]'
        if (typeof v === "object") {
          logWarn("Skipping object value in AP array", {
            apFieldId: apValue.id,
            ccFieldId: ccConfig.id,
            valueType: typeof v,
            value: JSON.stringify(v),
          });
          return false;
        }
        return String(v).trim() !== "";
      });

      if (ccConfig.allowMultipleValues) {
        // For multi-value CC fields, create separate value entries
        for (const value of validValues) {
          ccValues.push({ value: String(value).trim() });
        }
      } else {
        // For single-value CC fields, join with " | " separator
        if (validValues.length > 0) {
          const joinedValue = validValues.map(v => String(v).trim()).join(" | ");
          ccValues.push({ value: joinedValue });
        }
      }
    } else {
      // Handle single value (string or number) - but check for objects first
      if (typeof apValue.value === "object" && apValue.value !== null) {
        logError("AP value is an object but expected string/number - this is the source of '[object Object]' error", {
          apFieldId: apValue.id,
          ccFieldId: ccConfig.id,
          valueType: typeof apValue.value,
          value: JSON.stringify(apValue.value),
          isArray: Array.isArray(apValue.value),
          objectKeys: typeof apValue.value === 'object' ? Object.keys(apValue.value) : null,
          expectedFormat: "AP value should be string, number, or array for non-TEXTBOX_LIST fields",
        });
        // Skip object values that can't be converted to meaningful strings
        return null;
      }

      const stringValue = String(apValue.value).trim();
      if (stringValue !== "" && stringValue !== "[object Object]") {
        if (ccConfig.allowMultipleValues && stringValue.includes(" | ")) {
          // Split multi-value string for CC multi-value fields
          const parts = stringValue.split(" | ").map(p => p.trim()).filter(p => p !== "");
          for (const part of parts) {
            ccValues.push({ value: part });
          }
        } else {
          // Single value
          ccValues.push({ value: stringValue });
        }
      } else if (stringValue === "[object Object]") {
        logError("Detected '[object Object]' string conversion - this indicates an object was incorrectly converted to string", {
          apFieldId: apValue.id,
          ccFieldId: ccConfig.id,
          originalValueType: typeof apValue.value,
          originalValue: apValue.value,
        });
      }
    }
  }

  // Handle option mapping for CC fields with allowedValues
  if (ccConfig.allowedValues && ccConfig.allowedValues.length > 0) {
    // Try to map string values back to option IDs
    const mappedValues: Array<{ id?: number; value?: string }> = [];

    for (const ccValue of ccValues) {
      if (!ccValue.value) continue;

      // Try to find matching option by value (exact match first)
      let matchingOption = ccConfig.allowedValues.find(
        option => option.value === ccValue.value
      );

      // If no exact match, try case-insensitive match
      if (!matchingOption) {
        matchingOption = ccConfig.allowedValues.find(
          option => option.value && option.value.toLowerCase().trim() === ccValue.value.toLowerCase().trim()
        );
      }

      if (matchingOption) {
        // Use option ID if found
        mappedValues.push({ id: matchingOption.id });
        logDebug("Mapped AP value to CC option ID", {
          apValue: ccValue.value,
          ccOptionId: matchingOption.id,
          ccOptionValue: matchingOption.value,
        });
      } else {
        // Keep as string value if no matching option
        mappedValues.push({ value: ccValue.value });
        logDebug("No matching CC option found, keeping as string value", {
          apValue: ccValue.value,
          availableOptions: ccConfig.allowedValues.map(opt => opt.value),
        });
      }
    }

    // Use mapped values if any were found
    if (mappedValues.length > 0) {
      ccValues.length = 0; // Clear original array
      ccValues.push(...mappedValues);
    }
  }

  // Return null if no valid values were extracted
  if (ccValues.length === 0) {
    logDebug("No valid values extracted for CC field", {
      apFieldId: apValue.id,
      ccFieldId: ccConfig.id,
    });
    return null;
  }

  const result: CCCustomFieldValue = {
    values: ccValues,
    field: {
      id: ccConfig.id,
    },
  };

  logCustomField("AP→CC Value conversion - complete", ccConfig.name, {
    apFieldId: apValue.id,
    ccFieldId: ccConfig.id,
    resultValuesCount: ccValues.length,
    resultStructure: ccValues.map(v => ({
      hasId: "id" in v,
      hasValue: "value" in v,
    })),
  });

  return result;
}

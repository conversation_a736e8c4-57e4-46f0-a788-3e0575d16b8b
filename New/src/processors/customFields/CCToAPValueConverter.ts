import apiClient from "@/apiClient";
import { getDb, dbSchema } from "@/database";
import { eq } from "drizzle-orm";
import { logDebug, logWarn, logError, logCustomField } from "@/utils/logger";
import type { APGetCustomFieldType, GetCCPatientCustomField } from "@/type";
import { applyCCToAPConversion, type APConvertedValue } from "./CCToAPConversionLogic";

// APConvertedValue type is now imported from CCToAPConversionLogic






/**
 * Convert a CC custom field value into AP-compatible value using database-first field resolution.
 *
 * - Uses field information embedded in the value object to identify the source field
 * - Queries the database to find the corresponding target field mapping
 * - Implements smart API caching strategy (24-hour cache)
 * - Applies value conversion rules consistent with DATA-TYPE-MAP.md and repo preferences
 * - Handles multi-value (TEXTBOX_LIST, MULTIPLE_OPTIONS/CHECKBOX) and option mapping
 * - Filters useless values (empty strings) and gracefully handles mismatches
 */
export async function convertCCToAPValue(
  ccValue: GetCCPatientCustomField,
): Promise<APConvertedValue> {
  if (!ccValue?.field?.id) {
    logWarn("convertCCToAPValue called without valid CC field ID in value object");
    return null;
  }

  try {
    const db = getDb();

    // Query database to find the corresponding AP field mapping
    const mappingRows = await db
      .select()
      .from(dbSchema.customFields)
      .where(eq(dbSchema.customFields.ccId, ccValue.field.id))
      .limit(1);

    if (mappingRows.length === 0) {
      logWarn("No field mapping found for CC field", {
        ccFieldId: ccValue.field.id,
        ccFieldName: ccValue.field.name,
      });
      return null;
    }

    const mapping = mappingRows[0];
    if (!mapping.apId) {
      logWarn("No AP field ID found in mapping", {
        ccFieldId: ccValue.field.id,
        mappingId: mapping.id,
      });
      return null;
    }

    // Smart API caching strategy - check if configuration is fresh (within 24 hours)
    const now = new Date();
    const cacheAge = now.getTime() - new Date(mapping.updatedAt).getTime();
    const cacheMaxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    let field: APGetCustomFieldType;

    if (mapping.apConfig && cacheAge < cacheMaxAge) {
      // Use cached configuration
      field = mapping.apConfig as APGetCustomFieldType;
      logDebug("Using cached AP field configuration", {
        apFieldId: mapping.apId,
        cacheAgeHours: Math.round(cacheAge / (60 * 60 * 1000)),
      });
    } else {
      // Fetch fresh configuration from API
      logDebug("Fetching fresh AP field configuration from API", {
        apFieldId: mapping.apId,
        cacheAgeHours: cacheAge > 0 ? Math.round(cacheAge / (60 * 60 * 1000)) : 0,
        reason: mapping.apConfig ? "cache_expired" : "no_cache",
      });

      field = await apiClient.ap.apCustomfield.get(mapping.apId, true);

      // Update the database record with fresh configuration and timestamp
      await db
        .update(dbSchema.customFields)
        .set({
          apConfig: field,
          updatedAt: now,
        })
        .where(eq(dbSchema.customFields.id, mapping.id));

      logDebug("Updated database with fresh AP field configuration", {
        apFieldId: mapping.apId,
        mappingId: mapping.id,
      });
    }
    // Use the pure conversion logic utility function
    return applyCCToAPConversion(ccValue, field, mapping.apId);

  } catch (error) {
    logError("convertCCToAPValue failed", {
      ccFieldId: ccValue.field?.id,
      error,
    });
    return null;
  }
}

export default convertCCToAPValue;


import { getDb, dbSchema } from "@/database";
import { eq } from "drizzle-orm";
import { logDebug, logWarn, logError, logCustomField } from "@/utils/logger";
import apiClient from "@/apiClient";
import type { GetCCCustomField } from "@/type";
import { applyAPToCCConversion, type CCCustomFieldValue, type APCustomFieldValue } from "./APToCCConversionLogic";

// Types are now imported from APToCCConversionLogic

/**
 * Convert an AP custom field value back to CC format using database-first field resolution.
 *
 * This function:
 * - Uses the AP field ID from the value object to identify the source field
 * - Queries the database to find the corresponding target field mapping
 * - Implements smart API caching strategy (24-hour cache)
 * - Converts the AP value format back to the CC-expected format
 * - Handles TEXTBOX_LIST `field_value` objects by converting them to CC multi-value arrays
 * - Handles AP option values by mapping them back to CC option IDs/values as needed
 * - Returns the value in the format expected by CC's patient custom field update API
 */
export async function convertAPToCCValue(
  apValue: APCustomFieldValue
): Promise<CCCustomFieldValue | null> {
  // Enhanced logging to debug the '[object Object]' issue
  logDebug("convertAPToCCValue called with value", {
    apValueId: apValue?.id,
    apValueType: typeof apValue,
    apValueKeys: apValue ? Object.keys(apValue) : [],
    hasValue: "value" in (apValue || {}),
    hasFieldValue: "field_value" in (apValue || {}),
    valueType: typeof apValue?.value,
    fieldValueType: typeof apValue?.field_value,
    rawApValue: JSON.stringify(apValue, null, 2),
  });

  if (!apValue?.id) {
    logWarn("convertAPToCCValue called without valid AP field ID in value object", {
      hasApValueId: !!apValue?.id,
      apValue: JSON.stringify(apValue, null, 2),
    });
    return null;
  }

  try {
    const db = getDb();

    // Query database to find the corresponding CC field mapping
    const mappingRows = await db
      .select()
      .from(dbSchema.customFields)
      .where(eq(dbSchema.customFields.apId, apValue.id))
      .limit(1);

    if (mappingRows.length === 0) {
      logWarn("No field mapping found for AP field", {
        name: apValue.id, // This matches the error format you showed
        apFieldId: apValue.id,
        apValue: JSON.stringify(apValue, null, 2),
      });
      return null;
    }

    const mapping = mappingRows[0];
    if (!mapping.ccId) {
      logWarn("No CC field ID found in mapping", {
        apFieldId: apValue.id,
        mappingId: mapping.id,
      });
      return null;
    }

    // Smart API caching strategy - check if configuration is fresh (within 24 hours)
    const now = new Date();
    const cacheAge = now.getTime() - new Date(mapping.updatedAt).getTime();
    const cacheMaxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    let ccConfig: GetCCCustomField;

    if (mapping.ccConfig && cacheAge < cacheMaxAge) {
      // Use cached configuration
      ccConfig = mapping.ccConfig as GetCCCustomField;
      logDebug("Using cached CC field configuration", {
        ccFieldId: mapping.ccId,
        cacheAgeHours: Math.round(cacheAge / (60 * 60 * 1000)),
      });
    } else {
      // Fetch fresh configuration from API
      logDebug("Fetching fresh CC field configuration from API", {
        ccFieldId: mapping.ccId,
        cacheAgeHours: cacheAge > 0 ? Math.round(cacheAge / (60 * 60 * 1000)) : 0,
        reason: mapping.ccConfig ? "cache_expired" : "no_cache",
      });

      ccConfig = await apiClient.cc.ccCustomfieldReq.get(mapping.ccId, true);

      // Update the database record with fresh configuration and timestamp
      await db
        .update(dbSchema.customFields)
        .set({
          ccConfig: ccConfig,
          updatedAt: now,
        })
        .where(eq(dbSchema.customFields.id, mapping.id));

      logDebug("Updated database with fresh CC field configuration", {
        ccFieldId: mapping.ccId,
        mappingId: mapping.id,
      });
    }

    // Use the pure conversion logic utility function
    return applyAPToCCConversion(apValue, ccConfig);


  } catch (error) {
    logError("convertAPToCCValue failed", {
      apFieldId: apValue.id,
      error,
    });
    return null;
  }
}

export default convertAPToCCValue;
